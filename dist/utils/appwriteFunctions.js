"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyAppwriteUser = verifyAppwriteUser;
const node_appwrite_1 = require("node-appwrite");
async function verifyAppwriteUser(jwtToken) {
    const client = new node_appwrite_1.Client()
        .setEndpoint(process.env.APPWRITE_ENDPOINT)
        .setProject(process.env.APPWRITE_PROJECT_ID);
    try {
        const account = new node_appwrite_1.Account(client);
        client.setJWT(jwtToken);
        const user = await account.get();
        return user;
    }
    catch (err) {
        return null;
    }
}
