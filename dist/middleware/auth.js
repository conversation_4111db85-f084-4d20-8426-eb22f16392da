"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticateUser = void 0;
const appwriteFunctions_1 = require("../utils/appwriteFunctions");
const authenticateUser = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader) {
            res.status(401).json({ error: "Missing token" });
            return;
        }
        const jwtToken = authHeader.trim().split(" ")[1];
        console.log("Received JWT token:", jwtToken);
        if (!jwtToken) {
            res.status(401).json({ error: "Missing token" });
            return;
        }
        const user = await (0, appwriteFunctions_1.verifyAppwriteUser)(jwtToken);
        console.log("Authenticated user:", user);
        if (!user) {
            res.status(401).json({ error: "Invalid or expired token" });
            return;
        }
        console.log("User ID:", user.$id);
        // const userDetails = await findById(user.$id);
        // console.log("User details:", userDetails);
        // if (!userDetails) {
        //   res.status(404).json({ error: "User not found1" });
        //   return;
        // }
        // console.log("User details found:", userDetails);
        req.user = {
            id: user.$id,
            type: "SEEKER",
            email: user.email,
        };
        // console.log("User set in request:", req.user);
        next();
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ error: "Internal server error" });
    }
};
exports.authenticateUser = authenticateUser;
