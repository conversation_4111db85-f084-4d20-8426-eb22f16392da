"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.providerAuthMiddleware = void 0;
const providerAuthMiddleware = async (req, res, next) => {
    try {
        const user = req.user;
        if (!user) {
            res
                .status(401)
                .json({ message: "Unauthorized: Authentication required" });
            return;
        }
        if (user.type !== USER_TYPES.PROVIDER) {
            res.status(403).json({ message: "Forbidden: Provider access only" });
            return;
        }
        next();
    }
    catch (error) {
        console.error("Error in providerAuthMiddleware:", error);
        res.status(500).json({ message: "Internal server error" });
    }
};
exports.providerAuthMiddleware = providerAuthMiddleware;
