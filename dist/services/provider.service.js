"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.findVerifiedProviders = exports.updateVerificationStatus = exports.getAllProviders = exports.deleteProvider = exports.updateProviderByAppwriteId = exports.updateProvider = exports.findById = exports.findByAppwriteId = exports.createProvider = void 0;
const mongodb_1 = require("mongodb");
const db_1 = require("../config/db");
const providerCollection = () => db_1.db.collection("providers");
const createProvider = async (providerData) => {
    // Check if provider with this appwriteId already exists
    const existingProvider = await (0, exports.findByAppwriteId)(providerData.appwriteId);
    if (existingProvider) {
        throw new Error("Provider already exists");
    }
    const newProvider = {
        ...providerData,
        verificationStatus: "pending",
        phoneVerified: false,
        termsAccepted: true,
        privacyPolicyAccepted: true,
        profileCompletionSteps: {
            personalAccount: true,
            companyAddress: false,
            brandingVerification: false,
        },
        profileCompletionPercentage: 33, // 1 out of 3 steps completed
        isActive: true,
        isDeleted: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const result = await providerCollection().insertOne(newProvider);
    return { ...newProvider, _id: result.insertedId.toString() };
};
exports.createProvider = createProvider;
const findByAppwriteId = async (appwriteId) => {
    return await providerCollection().findOne({
        appwriteId,
        isDeleted: false,
    });
};
exports.findByAppwriteId = findByAppwriteId;
const findById = async (providerId) => {
    if (!mongodb_1.ObjectId.isValid(providerId)) {
        return null;
    }
    return await providerCollection().findOne({
        _id: new mongodb_1.ObjectId(providerId),
        isDeleted: false,
    });
};
exports.findById = findById;
const updateProvider = async (providerId, updateData) => {
    if (!mongodb_1.ObjectId.isValid(providerId)) {
        return null;
    }
    const updatedProvider = {
        $set: {
            ...updateData,
            updatedAt: new Date(),
        },
    };
    await providerCollection().updateOne({ _id: new mongodb_1.ObjectId(providerId) }, updatedProvider);
    return await (0, exports.findById)(providerId);
};
exports.updateProvider = updateProvider;
const updateProviderByAppwriteId = async (appwriteId, updateData) => {
    const provider = await (0, exports.findByAppwriteId)(appwriteId);
    if (!provider) {
        return null;
    }
    const updatedProvider = {
        $set: {
            ...updateData,
            updatedAt: new Date(),
        },
    };
    await providerCollection().updateOne({ appwriteId }, updatedProvider);
    return await (0, exports.findByAppwriteId)(appwriteId);
};
exports.updateProviderByAppwriteId = updateProviderByAppwriteId;
const deleteProvider = async (providerId) => {
    if (!mongodb_1.ObjectId.isValid(providerId)) {
        return false;
    }
    const result = await providerCollection().updateOne({ _id: new mongodb_1.ObjectId(providerId) }, { $set: { isDeleted: true, updatedAt: new Date() } });
    return result.modifiedCount > 0;
};
exports.deleteProvider = deleteProvider;
const getAllProviders = async (page = 1, limit = 10, filters = {}) => {
    const skip = (page - 1) * limit;
    const query = { isDeleted: false, ...filters };
    const providers = await providerCollection()
        .find(query)
        .skip(skip)
        .limit(limit)
        .toArray();
    const total = await providerCollection().countDocuments(query);
    return { providers, total };
};
exports.getAllProviders = getAllProviders;
const updateVerificationStatus = async (providerId, status, notes, verifiedBy) => {
    if (!mongodb_1.ObjectId.isValid(providerId)) {
        return null;
    }
    const updateData = {
        verificationStatus: status,
        updatedAt: new Date(),
    };
    if (status === "verified") {
        updateData.verificationCompletedAt = new Date();
    }
    if (notes) {
        updateData.verificationNotes = notes;
    }
    if (verifiedBy) {
        updateData.verifiedBy = new mongodb_1.ObjectId(verifiedBy);
    }
    await providerCollection().updateOne({ _id: new mongodb_1.ObjectId(providerId) }, { $set: updateData });
    return await (0, exports.findById)(providerId);
};
exports.updateVerificationStatus = updateVerificationStatus;
const findVerifiedProviders = async () => {
    return await providerCollection()
        .find({
        isDeleted: false,
        verificationStatus: "verified",
    })
        .toArray();
};
exports.findVerifiedProviders = findVerifiedProviders;
