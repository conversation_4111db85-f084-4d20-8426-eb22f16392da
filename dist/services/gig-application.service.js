"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.withdrawApplication = exports.findRecentApplications = exports.getApplicationStatistics = exports.findApplicationsByStatus = exports.findApplicationsByProvider = exports.findApplicationsByGig = exports.findApplicationsBySeeker = exports.deleteGigApplication = exports.updateApplicationStatus = exports.updateGigApplication = exports.findBySeekerAndGig = exports.findById = exports.createGigApplication = void 0;
const mongodb_1 = require("mongodb");
const db_1 = require("../config/db");
const gigApplicationCollection = () => db_1.db.collection("gig_applications");
const createGigApplication = async (applicationData) => {
    // Check if seeker has already applied for this gig
    const existingApplication = await (0, exports.findBySeekerAndGig)(applicationData.seekerId, applicationData.gigId);
    if (existingApplication) {
        throw new Error("Seeker has already applied for this gig");
    }
    const newApplication = {
        ...applicationData,
        seekerId: new mongodb_1.ObjectId(applicationData.seekerId),
        gigId: new mongodb_1.ObjectId(applicationData.gigId),
        appliedAt: new Date(),
        status: "applied",
        files: applicationData.files || [],
        isDeleted: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const result = await gigApplicationCollection().insertOne(newApplication);
    return { ...newApplication, _id: result.insertedId.toString() };
};
exports.createGigApplication = createGigApplication;
const findById = async (applicationId) => {
    if (!mongodb_1.ObjectId.isValid(applicationId)) {
        return null;
    }
    return await gigApplicationCollection().findOne({
        _id: new mongodb_1.ObjectId(applicationId),
        isDeleted: false,
    });
};
exports.findById = findById;
const findBySeekerAndGig = async (seekerId, gigId) => {
    if (!mongodb_1.ObjectId.isValid(seekerId) || !mongodb_1.ObjectId.isValid(gigId)) {
        return null;
    }
    return await gigApplicationCollection().findOne({
        seekerId: new mongodb_1.ObjectId(seekerId),
        gigId: new mongodb_1.ObjectId(gigId),
        isDeleted: false,
    });
};
exports.findBySeekerAndGig = findBySeekerAndGig;
const updateGigApplication = async (applicationId, updateData) => {
    if (!mongodb_1.ObjectId.isValid(applicationId)) {
        return null;
    }
    const updatedApplication = {
        $set: {
            ...updateData,
            updatedAt: new Date(),
        },
    };
    await gigApplicationCollection().updateOne({ _id: new mongodb_1.ObjectId(applicationId) }, updatedApplication);
    return await (0, exports.findById)(applicationId);
};
exports.updateGigApplication = updateGigApplication;
const updateApplicationStatus = async (applicationId, status, reviewedBy, notes) => {
    if (!mongodb_1.ObjectId.isValid(applicationId)) {
        return null;
    }
    const updateData = {
        status,
        reviewedAt: new Date(),
        updatedAt: new Date(),
    };
    if (reviewedBy && mongodb_1.ObjectId.isValid(reviewedBy)) {
        updateData.reviewedBy = new mongodb_1.ObjectId(reviewedBy);
    }
    if (notes) {
        updateData.notes = notes;
    }
    await gigApplicationCollection().updateOne({ _id: new mongodb_1.ObjectId(applicationId) }, { $set: updateData });
    return await (0, exports.findById)(applicationId);
};
exports.updateApplicationStatus = updateApplicationStatus;
const deleteGigApplication = async (applicationId) => {
    if (!mongodb_1.ObjectId.isValid(applicationId)) {
        return false;
    }
    const result = await gigApplicationCollection().updateOne({ _id: new mongodb_1.ObjectId(applicationId) }, { $set: { isDeleted: true, updatedAt: new Date() } });
    return result.modifiedCount > 0;
};
exports.deleteGigApplication = deleteGigApplication;
const findApplicationsBySeeker = async (seekerId) => {
    if (!mongodb_1.ObjectId.isValid(seekerId)) {
        return [];
    }
    return await gigApplicationCollection()
        .find({
        seekerId: new mongodb_1.ObjectId(seekerId),
        isDeleted: false,
    })
        .sort({ appliedAt: -1 })
        .toArray();
};
exports.findApplicationsBySeeker = findApplicationsBySeeker;
const findApplicationsByGig = async (gigId) => {
    if (!mongodb_1.ObjectId.isValid(gigId)) {
        return [];
    }
    return await gigApplicationCollection()
        .find({
        gigId: new mongodb_1.ObjectId(gigId),
        isDeleted: false,
    })
        .sort({ appliedAt: -1 })
        .toArray();
};
exports.findApplicationsByGig = findApplicationsByGig;
const findApplicationsByProvider = async (providerGigIds) => {
    const validGigIds = providerGigIds
        .filter(id => mongodb_1.ObjectId.isValid(id))
        .map(id => new mongodb_1.ObjectId(id));
    if (validGigIds.length === 0) {
        return [];
    }
    return await gigApplicationCollection()
        .find({
        gigId: { $in: validGigIds },
        isDeleted: false,
    })
        .sort({ appliedAt: -1 })
        .toArray();
};
exports.findApplicationsByProvider = findApplicationsByProvider;
const findApplicationsByStatus = async (status) => {
    return await gigApplicationCollection()
        .find({
        status: status,
        isDeleted: false,
    })
        .sort({ appliedAt: -1 })
        .toArray();
};
exports.findApplicationsByStatus = findApplicationsByStatus;
const getApplicationStatistics = async (gigId) => {
    const matchStage = { isDeleted: false };
    if (gigId && mongodb_1.ObjectId.isValid(gigId)) {
        matchStage.gigId = new mongodb_1.ObjectId(gigId);
    }
    const pipeline = [
        { $match: matchStage },
        {
            $group: {
                _id: "$status",
                count: { $sum: 1 },
            },
        },
    ];
    const statusCounts = await gigApplicationCollection().aggregate(pipeline).toArray();
    const total = await gigApplicationCollection().countDocuments(matchStage);
    const byStatus = statusCounts.map(item => ({
        status: item._id,
        count: item.count,
    }));
    return { total, byStatus };
};
exports.getApplicationStatistics = getApplicationStatistics;
const findRecentApplications = async (limit = 10) => {
    return await gigApplicationCollection()
        .find({ isDeleted: false })
        .sort({ appliedAt: -1 })
        .limit(limit)
        .toArray();
};
exports.findRecentApplications = findRecentApplications;
const withdrawApplication = async (applicationId, seekerId) => {
    if (!mongodb_1.ObjectId.isValid(applicationId) || !mongodb_1.ObjectId.isValid(seekerId)) {
        return false;
    }
    const result = await gigApplicationCollection().updateOne({
        _id: new mongodb_1.ObjectId(applicationId),
        seekerId: new mongodb_1.ObjectId(seekerId),
        status: { $in: ["applied", "under_review"] }, // Can only withdraw if not yet processed
    }, {
        $set: {
            status: "withdrawn",
            updatedAt: new Date(),
        },
    });
    return result.modifiedCount > 0;
};
exports.withdrawApplication = withdrawApplication;
