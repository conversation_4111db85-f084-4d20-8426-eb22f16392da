"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.findActiveGigs = exports.searchGigs = exports.decrementApplicationCount = exports.incrementApplicationCount = exports.findGigsBySalaryRange = exports.findGigsByJobType = exports.findGigsByLocation = exports.findGigsByProvider = exports.getAllGigs = exports.deleteGig = exports.updateGig = exports.findById = exports.createGig = void 0;
const mongodb_1 = require("mongodb");
const db_1 = require("../config/db");
const gigCollection = () => db_1.db.collection("gigs");
const createGig = async (gigData) => {
    const newGig = {
        ...gigData,
        providerId: new mongodb_1.ObjectId(gigData.providerId),
        numberOfPeopleApplied: 0,
        shopImages: gigData.shopImages || [],
        specialization: gigData.specialization || [],
        facilities: gigData.facilities || [],
        minimumGiggleGrade: gigData.minimumGiggleGrade || 0,
        isActive: true,
        isDeleted: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const result = await gigCollection().insertOne(newGig);
    return { ...newGig, _id: result.insertedId.toString() };
};
exports.createGig = createGig;
const findById = async (gigId) => {
    if (!mongodb_1.ObjectId.isValid(gigId)) {
        return null;
    }
    return await gigCollection().findOne({
        _id: new mongodb_1.ObjectId(gigId),
        isDeleted: false,
    });
};
exports.findById = findById;
const updateGig = async (gigId, updateData) => {
    if (!mongodb_1.ObjectId.isValid(gigId)) {
        return null;
    }
    const updatedGig = {
        $set: {
            ...updateData,
            updatedAt: new Date(),
        },
    };
    await gigCollection().updateOne({ _id: new mongodb_1.ObjectId(gigId) }, updatedGig);
    return await (0, exports.findById)(gigId);
};
exports.updateGig = updateGig;
const deleteGig = async (gigId) => {
    if (!mongodb_1.ObjectId.isValid(gigId)) {
        return false;
    }
    const result = await gigCollection().updateOne({ _id: new mongodb_1.ObjectId(gigId) }, { $set: { isDeleted: true, updatedAt: new Date() } });
    return result.modifiedCount > 0;
};
exports.deleteGig = deleteGig;
const getAllGigs = async (page = 1, limit = 10, filters = {}) => {
    const skip = (page - 1) * limit;
    const query = { isDeleted: false, isActive: true, ...filters };
    const gigs = await gigCollection()
        .find(query)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .toArray();
    const total = await gigCollection().countDocuments(query);
    return { gigs, total };
};
exports.getAllGigs = getAllGigs;
const findGigsByProvider = async (providerId) => {
    if (!mongodb_1.ObjectId.isValid(providerId)) {
        return [];
    }
    return await gigCollection()
        .find({
        providerId: new mongodb_1.ObjectId(providerId),
        isDeleted: false,
    })
        .sort({ createdAt: -1 })
        .toArray();
};
exports.findGigsByProvider = findGigsByProvider;
const findGigsByLocation = async (location) => {
    return await gigCollection()
        .find({
        isDeleted: false,
        isActive: true,
        $or: [
            { "location.city": { $regex: location, $options: "i" } },
            { "location.state": { $regex: location, $options: "i" } },
            { "location.country": { $regex: location, $options: "i" } },
        ],
    })
        .sort({ createdAt: -1 })
        .toArray();
};
exports.findGigsByLocation = findGigsByLocation;
const findGigsByJobType = async (jobType) => {
    return await gigCollection()
        .find({
        isDeleted: false,
        isActive: true,
        jobType: jobType,
    })
        .sort({ createdAt: -1 })
        .toArray();
};
exports.findGigsByJobType = findGigsByJobType;
const findGigsBySalaryRange = async (minSalary, maxSalary, currency = "INR") => {
    return await gigCollection()
        .find({
        isDeleted: false,
        isActive: true,
        "salary.currency": currency,
        "salary.min": { $gte: minSalary },
        "salary.max": { $lte: maxSalary },
    })
        .sort({ createdAt: -1 })
        .toArray();
};
exports.findGigsBySalaryRange = findGigsBySalaryRange;
const incrementApplicationCount = async (gigId) => {
    if (!mongodb_1.ObjectId.isValid(gigId)) {
        return false;
    }
    const result = await gigCollection().updateOne({ _id: new mongodb_1.ObjectId(gigId) }, {
        $inc: { numberOfPeopleApplied: 1 },
        $set: { updatedAt: new Date() },
    });
    return result.modifiedCount > 0;
};
exports.incrementApplicationCount = incrementApplicationCount;
const decrementApplicationCount = async (gigId) => {
    if (!mongodb_1.ObjectId.isValid(gigId)) {
        return false;
    }
    const result = await gigCollection().updateOne({ _id: new mongodb_1.ObjectId(gigId) }, {
        $inc: { numberOfPeopleApplied: -1 },
        $set: { updatedAt: new Date() },
    });
    return result.modifiedCount > 0;
};
exports.decrementApplicationCount = decrementApplicationCount;
const searchGigs = async (searchTerm, page = 1, limit = 10) => {
    const skip = (page - 1) * limit;
    const searchRegex = new RegExp(searchTerm, "i");
    const query = {
        isDeleted: false,
        isActive: true,
        $or: [
            { title: searchRegex },
            { description: searchRegex },
            { companyName: searchRegex },
            { positionOffered: searchRegex },
            { specialization: { $in: [searchTerm] } },
        ],
    };
    const gigs = await gigCollection()
        .find(query)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .toArray();
    const total = await gigCollection().countDocuments(query);
    return { gigs, total };
};
exports.searchGigs = searchGigs;
const findActiveGigs = async () => {
    return await gigCollection()
        .find({
        isDeleted: false,
        isActive: true,
    })
        .sort({ createdAt: -1 })
        .toArray();
};
exports.findActiveGigs = findActiveGigs;
