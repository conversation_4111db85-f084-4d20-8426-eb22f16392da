"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const express_1 = __importDefault(require("express"));
const db_1 = require("./config/db");
const routes_1 = require("./routes");
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 5000;
const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(",") || [];
app.use((0, cors_1.default)({
    origin: (origin, callback) => {
        if (!origin)
            return callback(null, true);
        if (allowedOrigins.includes(origin)) {
            return callback(null, true);
        }
        return callback(new Error("Not allowed by CORS"));
    },
    credentials: true,
}));
app.use(express_1.default.json());
// Register all routes
(0, routes_1.registerRoutes)(app);
// Start server after DB connects
(0, db_1.connectToDB)().then(() => {
    const server = app.listen(PORT, () => {
        console.log(`Server running on port ${PORT}`);
    });
    // Handle graceful shutdown
    process.on("SIGINT", async () => {
        console.log("[SIGINT] Shutting down server...");
        server.close(async () => {
            await (0, db_1.closeDBConnection)();
            process.exit(0);
        });
    });
    process.on("SIGTERM", async () => {
        console.log("[SIGTERM] Shutting down server...");
        server.close(async () => {
            await (0, db_1.closeDBConnection)();
            process.exit(0);
        });
    });
});
