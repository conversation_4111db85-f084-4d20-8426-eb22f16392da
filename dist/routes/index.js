"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerRoutes = registerRoutes;
const auth_1 = require("../middleware/auth");
const provider_auth_middleware_1 = require("../middleware/provider-auth.middleware");
const seeker_auth_middleware_1 = require("../middleware/seeker-auth.middleware");
const example_routes_1 = __importDefault(require("./example.routes"));
const user_routes_1 = __importDefault(require("./user.routes"));
function registerRoutes(app) {
    // Common routes without user type restriction
    app.use("/api/users", user_routes_1.default);
    // Seeker routes with authentication and seeker validation
    app.use("/api/seeker", auth_1.authenticateUser, seeker_auth_middleware_1.seekerAuthMiddleware, example_routes_1.default);
    // Provider routes with authentication and provider validation
    app.use("/api/provider", auth_1.authenticateUser, provider_auth_middleware_1.providerAuthMiddleware, example_routes_1.default);
}
