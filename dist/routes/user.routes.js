"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const user_controller_1 = require("../controllers/user.controller");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// All routes are protected and require authentication
router.post("/register", auth_1.authenticateUser, user_controller_1.registerUser);
router.get("/profile", auth_1.authenticateUser, user_controller_1.getUserProfile);
router.patch("/profile", auth_1.authenticateUser, user_controller_1.updateUserProfile);
router.delete("/profile", auth_1.authenticateUser, user_controller_1.deleteUserProfile);
router.get("/verify-user", user_controller_1.verifyUser);
router.post("/send-verification-email", auth_1.authenticateUser, user_controller_1.sendVerificationEmail);
exports.default = router;
