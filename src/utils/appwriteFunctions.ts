import { Account, Client } from "node-appwrite";

export async function verifyAppwriteUser(jwtToken: string) {
  const client = new Client()
    .setEndpoint(process.env.APPWRITE_ENDPOINT!)
    .setProject(process.env.APPWRITE_PROJECT_ID!);

  try {
    const account = new Account(client);

    client.setJWT(jwtToken);
    const user = await account.get();
    return user;
  } catch (err) {
    return null;
  }
}
