import { ObjectId } from "mongodb";
import { Location } from "./user.model";

export type JobType = "full-time" | "part-time" | "contract" | "freelance" | "internship" | "temporary";
export type JobTrait = "remote" | "on-site" | "hybrid" | "flexible";

export interface Gig {
	_id?: string | ObjectId;
	title: string;
	description: string;
	salary: {
		min: number;
		max: number;
		currency: string;
		period: "hourly" | "daily" | "weekly" | "monthly" | "yearly";
	};
	numberOfOpenPositions: number;
	shopImages?: string[]; // Array of image URLs or file IDs
	location: Location;
	jobType: JobType;
	jobTrait: JobTrait;
	providerId: string; // Reference to Provider.provider_id
	numberOfPeopleApplied: number;
	companyName: string;
	positionOffered: string;
	minimumQualificationRequired: string;
	experience: {
		min: number; // in years
		max?: number; // in years
		description?: string;
	};
	specialization: string[];
	facilities: string[]; // e.g., ["health insurance", "flexible hours", "remote work"]
	minimumGiggleGrade: number; // 0-100 scale
	isActive: boolean;
	isDeleted: boolean;
	createdAt: Date;
	updatedAt: Date;
}

export interface CreateGigDto {
	title: string;
	description: string;
	salary: {
		min: number;
		max: number;
		currency: string;
		period: "hourly" | "daily" | "weekly" | "monthly" | "yearly";
	};
	numberOfOpenPositions: number;
	shopImages?: string[];
	location: Location;
	jobType: JobType;
	jobTrait: JobTrait;
	providerId: string;
	companyName: string;
	positionOffered: string;
	minimumQualificationRequired: string;
	experience: {
		min: number;
		max?: number;
		description?: string;
	};
	specialization: string[];
	facilities: string[];
	minimumGiggleGrade: number;
}

export interface UpdateGigDto {
	title?: string;
	description?: string;
	salary?: {
		min?: number;
		max?: number;
		currency?: string;
		period?: "hourly" | "daily" | "weekly" | "monthly" | "yearly";
	};
	numberOfOpenPositions?: number;
	shopImages?: string[];
	location?: Location;
	jobType?: JobType;
	jobTrait?: JobTrait;
	companyName?: string;
	positionOffered?: string;
	minimumQualificationRequired?: string;
	experience?: {
		min?: number;
		max?: number;
		description?: string;
	};
	specialization?: string[];
	facilities?: string[];
	minimumGiggleGrade?: number;
	isActive?: boolean;
}

// Helper type for MongoDB document with ObjectId
export type GigDocument = Omit<Gig, "_id"> & {
	_id: ObjectId;
};
