import { ObjectId } from "mongodb";

export interface FlnDetails {
	fluency: number; // 0-100 scale
	literacy: number; // 0-100 scale
	numeracy: number; // 0-100 scale
}

export interface FlnRecord {
	score: number; // Overall FLN score (0-100)
	details: FlnDetails;
	testDate: Date;
	validUntil?: Date; // Optional expiry date for the score
}

export interface Fln {
	_id?: string | ObjectId;
	seekerId: string | ObjectId; // Reference to Seeker._id - FLN scores are only for seekers
	flnScore: number; // Current/latest FLN score (0-100)
	details: FlnDetails; // Current/latest breakdown
	fluencyRecord: FlnRecord[]; // Historical records of FLN assessments
	createdAt: Date;
	updatedAt: Date;
	isDeleted: boolean;
}

export interface CreateFlnDto {
	userId: string;
	flnScore: number;
	details: FlnDetails;
	testDate?: Date;
	validUntil?: Date;
}

export interface UpdateFlnDto {
	flnScore?: number;
	details?: FlnDetails;
	newRecord?: {
		score: number;
		details: FlnDetails;
		testDate: Date;
		validUntil?: Date;
	};
}

// Helper type for MongoDB document with ObjectId
export type FlnDocument = Omit<Fln, "_id"> & {
	_id: ObjectId;
};
