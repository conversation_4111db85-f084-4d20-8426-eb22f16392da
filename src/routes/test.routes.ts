import express from "express";
import { Request, Response } from "express";
import { 
  createProvider, 
  findByAppwriteId as findProviderByAppwriteId,
  getAllProviders 
} from "../services/provider.service";
import { 
  createSeeker, 
  findByAppwriteId as findSeeker<PERSON><PERSON>AppwriteId,
  getAllSeekers 
} from "../services/seeker.service";
import { 
  createCompany, 
  findCompaniesByProvider 
} from "../services/company.service";
import { 
  createGig, 
  findGigsByProvider,
  getAllGigs 
} from "../services/gig.service";
import { authenticateUser } from "../middleware/auth";

const router = express.Router();

// Test Provider endpoints
router.post("/provider", authenticateUser, async (req: Request, res: Response) => {
  try {
    const providerData = {
      appwriteId: req.user.id,
      email: req.user.email,
      name: req.body.name || "Test Provider",
      phoneNumber: req.body.phoneNumber || "+**********",
      termsAccepted: true,
      privacyPolicyAccepted: true,
      ...req.body
    };

    const provider = await createProvider(providerData);
    res.status(201).json({
      success: true,
      message: "Provider created successfully",
      data: provider
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      message: error.message || "Failed to create provider"
    });
  }
});

router.get("/provider", authenticateUser, async (req: Request, res: Response) => {
  try {
    const provider = await findProviderByAppwriteId(req.user.id);
    if (!provider) {
      return res.status(404).json({
        success: false,
        message: "Provider not found"
      });
    }
    res.json({
      success: true,
      data: provider
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to get provider"
    });
  }
});

router.get("/providers", async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const result = await getAllProviders(page, limit);
    res.json({
      success: true,
      data: result
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to get providers"
    });
  }
});

// Test Seeker endpoints
router.post("/seeker", authenticateUser, async (req: Request, res: Response) => {
  try {
    const seekerData = {
      appwriteId: req.user.id,
      email: req.user.email,
      name: req.body.name || "Test Seeker",
      phoneNumber: req.body.phoneNumber || "+**********",
      educationLevel: req.body.educationLevel || "bachelor",
      employmentStatus: req.body.employmentStatus || "unemployed",
      ...req.body
    };

    const seeker = await createSeeker(seekerData);
    res.status(201).json({
      success: true,
      message: "Seeker created successfully",
      data: seeker
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      message: error.message || "Failed to create seeker"
    });
  }
});

router.get("/seeker", authenticateUser, async (req: Request, res: Response) => {
  try {
    const seeker = await findSeekerByAppwriteId(req.user.id);
    if (!seeker) {
      return res.status(404).json({
        success: false,
        message: "Seeker not found"
      });
    }
    res.json({
      success: true,
      data: seeker
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to get seeker"
    });
  }
});

router.get("/seekers", async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const result = await getAllSeekers(page, limit);
    res.json({
      success: true,
      data: result
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to get seekers"
    });
  }
});

// Test Company endpoints
router.post("/company", authenticateUser, async (req: Request, res: Response) => {
  try {
    // First find the provider
    const provider = await findProviderByAppwriteId(req.user.id);
    if (!provider) {
      return res.status(404).json({
        success: false,
        message: "Provider not found. Create a provider first."
      });
    }

    const companyData = {
      providerId: provider._id!.toString(),
      companyName: req.body.companyName || "Test Company",
      industry: req.body.industry || "technology",
      companySize: req.body.companySize || "startup",
      description: req.body.description || "A test company",
      ...req.body
    };

    const company = await createCompany(companyData);
    res.status(201).json({
      success: true,
      message: "Company created successfully",
      data: company
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      message: error.message || "Failed to create company"
    });
  }
});

// Test Gig endpoints
router.post("/gig", authenticateUser, async (req: Request, res: Response) => {
  try {
    // First find the provider
    const provider = await findProviderByAppwriteId(req.user.id);
    if (!provider) {
      return res.status(404).json({
        success: false,
        message: "Provider not found. Create a provider first."
      });
    }

    const gigData = {
      providerId: provider._id!.toString(),
      title: req.body.title || "Test Gig",
      description: req.body.description || "A test gig for testing",
      salary: {
        min: 1000,
        max: 3000,
        currency: "USD",
        period: "monthly"
      },
      numberOfOpenPositions: 1,
      location: {
        latitude: 40.7128,
        longitude: -74.0060,
        address: "New York, NY"
      },
      jobType: "full-time",
      jobTrait: "on-site",
      companyName: "Test Company",
      positionOffered: "Software Developer",
      minimumQualificationRequired: "Bachelor's degree",
      experience: {
        min: 1,
        max: 3,
        description: "1-3 years experience"
      },
      specialization: ["JavaScript", "TypeScript"],
      facilities: ["Health Insurance", "Flexible Hours"],
      minimumGiggleGrade: 3,
      ...req.body
    };

    const gig = await createGig(gigData);
    res.status(201).json({
      success: true,
      message: "Gig created successfully",
      data: gig
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      message: error.message || "Failed to create gig"
    });
  }
});

router.get("/gigs", async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const result = await getAllGigs(page, limit);
    res.json({
      success: true,
      data: result
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to get gigs"
    });
  }
});

export default router;
