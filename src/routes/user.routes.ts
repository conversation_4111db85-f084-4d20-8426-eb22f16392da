import express from "express";
import {
  deleteUserProfile,
  getUserProfile,
  registerUser,
  updateUserProfile,
  verifyUser,
  sendVerificationEmail,
} from "../controllers/user.controller";
import { authenticateUser } from "../middleware/auth";

const router = express.Router();

// All routes are protected and require authentication
router.post("/register", authenticateUser, registerUser);
router.get("/profile", authenticateUser, getUserProfile);
router.patch("/profile", authenticateUser, updateUserProfile);
router.delete("/profile", authenticateUser, deleteUserProfile);
router.get("/verify-user", verifyUser);
router.post(
  "/send-verification-email",
  authenticateUser,
  sendVerificationEmail
);

export default router;
