import { Collection, ObjectId } from "mongodb";
import { db } from "../config/db";
import { Provider, CreateProviderDto, UpdateProviderDto } from "../models/provider.model";

const providerCollection = (): Collection<Provider> => db.collection<Provider>("providers");

export const createProvider = async (providerData: CreateProviderDto): Promise<Provider> => {
	// Check if provider with this appwriteId already exists
	const existingProvider = await findByAppwriteId(providerData.appwriteId);
	if (existingProvider) {
		throw new Error("Provider with this Appwrite ID already exists");
	}

	const newProvider: Provider = {
		...providerData,
		phoneVerified: false,
		verificationStatus: "pending",
		isActive: true,
		isDeleted: false,
		createdAt: new Date(),
		updatedAt: new Date(),
	};

	const result = await providerCollection().insertOne(newProvider);
	return { ...newProvider, _id: result.insertedId.toString() };
};

export const findById = async (providerId: string): Promise<Provider | null> => {
	if (!ObjectId.isValid(providerId)) {
		return null;
	}
	return await providerCollection().findOne({
		_id: new ObjectId(providerId),
		isDeleted: false,
	});
};

export const findByAppwriteId = async (appwriteId: string): Promise<Provider | null> => {
	return await providerCollection().findOne({
		appwriteId,
		isDeleted: false,
	});
};



export const updateProvider = async (
	providerId: string,
	updateData: UpdateProviderDto
): Promise<Provider | null> => {
	if (!ObjectId.isValid(providerId)) {
		return null;
	}

	const updatedProvider = {
		$set: {
			...updateData,
			updatedAt: new Date(),
		},
	};

	await providerCollection().updateOne(
		{ _id: new ObjectId(providerId), isDeleted: false },
		updatedProvider
	);

	return await findById(providerId);
};

export const updateProviderByAppwriteId = async (
	appwriteId: string,
	updateData: UpdateProviderDto
): Promise<Provider | null> => {
	const updatedProvider = {
		$set: {
			...updateData,
			updatedAt: new Date(),
		},
	};

	await providerCollection().updateOne(
		{ appwriteId, isDeleted: false },
		updatedProvider
	);

	return await findByAppwriteId(appwriteId);
};

export const deleteProvider = async (providerId: string): Promise<boolean> => {
	if (!ObjectId.isValid(providerId)) {
		return false;
	}

	const result = await providerCollection().updateOne(
		{ _id: new ObjectId(providerId), isDeleted: false },
		{ $set: { isDeleted: true, updatedAt: new Date() } }
	);

	return result.modifiedCount > 0;
};

export const getAllProviders = async (
	page: number = 1,
	limit: number = 10,
	filters: any = {}
): Promise<{ providers: Provider[]; total: number }> => {
	const skip = (page - 1) * limit;
	const query = { isDeleted: false, ...filters };

	const providers = await providerCollection()
		.find(query)
		.skip(skip)
		.limit(limit)
		.toArray();

	const total = await providerCollection().countDocuments(query);

	return { providers, total };
};

export const updateVerificationStatus = async (
	providerId: string,
	status: "pending" | "in_review" | "verified" | "rejected",
	notes?: string,
	verifiedBy?: string
): Promise<Provider | null> => {
	if (!ObjectId.isValid(providerId)) {
		return null;
	}

	const updateData: any = {
		verificationStatus: status,
		updatedAt: new Date(),
	};

	if (status === "verified") {
		updateData.verificationCompletedAt = new Date();
	}

	if (notes) {
		updateData.verificationNotes = notes;
	}

	if (verifiedBy) {
		updateData.verifiedBy = new ObjectId(verifiedBy);
	}

	await providerCollection().updateOne(
		{ _id: new ObjectId(providerId), isDeleted: false },
		{ $set: updateData }
	);

	return await findById(providerId);
};

export const findVerifiedProviders = async (): Promise<Provider[]> => {
	return await providerCollection()
		.find({
			isDeleted: false,
			verificationStatus: "verified",
		})
		.toArray();
};
