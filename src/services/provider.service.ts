import { Collection } from "mongodb";
import { db } from "../config/db";
import { Provider, CreateProviderDto, UpdateProviderDto } from "../models/provider.model";

const providerCollection = (): Collection<Provider> => db.collection<Provider>("providers");

export const createProvider = async (providerData: CreateProviderDto): Promise<Provider> => {
	// Check if provider with this provider_id already exists
	const existingProvider = await findById(providerData.provider_id);
	if (existingProvider) {
		throw new Error("Provider already exists");
	}

	const newProvider: Provider = {
		...providerData,
		verificationStatus: "pending",
		phoneVerified: false,
		profileCompletionSteps: {
			personalAccount: true,
			companyAddress: false,
			brandingVerification: false,
		},
		profileCompletionPercentage: 33, // 1 out of 3 steps completed
		isActive: true,
		isDeleted: false,
		createdAt: new Date(),
		updatedAt: new Date(),
	};

	await providerCollection().insertOne(newProvider);
	return newProvider;
};

export const findById = async (provider_id: string): Promise<Provider | null> => {
	return await providerCollection().findOne({
		provider_id,
		isDeleted: false,
	});
};



export const updateProvider = async (
	provider_id: string,
	updateData: UpdateProviderDto
): Promise<Provider | null> => {
	const updatedProvider = {
		$set: {
			...updateData,
			updatedAt: new Date(),
		},
	};

	await providerCollection().updateOne(
		{ provider_id, isDeleted: false },
		updatedProvider
	);

	return await findById(provider_id);
};



export const deleteProvider = async (provider_id: string): Promise<boolean> => {
	const result = await providerCollection().updateOne(
		{ provider_id, isDeleted: false },
		{ $set: { isDeleted: true, updatedAt: new Date() } }
	);

	return result.modifiedCount > 0;
};

export const getAllProviders = async (
	page: number = 1,
	limit: number = 10,
	filters: any = {}
): Promise<{ providers: Provider[]; total: number }> => {
	const skip = (page - 1) * limit;
	const query = { isDeleted: false, ...filters };

	const providers = await providerCollection()
		.find(query)
		.skip(skip)
		.limit(limit)
		.toArray();

	const total = await providerCollection().countDocuments(query);

	return { providers, total };
};

export const updateVerificationStatus = async (
	provider_id: string,
	status: "pending" | "in_review" | "verified" | "rejected",
	notes?: string,
	verifiedBy?: string
): Promise<Provider | null> => {
	const updateData: any = {
		verificationStatus: status,
		updatedAt: new Date(),
	};

	if (status === "verified") {
		updateData.verificationCompletedAt = new Date();
	}

	if (notes) {
		updateData.verificationNotes = notes;
	}

	if (verifiedBy) {
		updateData.verifiedBy = new ObjectId(verifiedBy);
	}

	await providerCollection().updateOne(
		{ provider_id, isDeleted: false },
		{ $set: updateData }
	);

	return await findById(provider_id);
};

export const findVerifiedProviders = async (): Promise<Provider[]> => {
	return await providerCollection()
		.find({
			isDeleted: false,
			verificationStatus: "verified",
		})
		.toArray();
};
