import { Collection, ObjectId } from "mongodb";
import { db } from "../config/db";
import { Seeker, CreateSeekerDto, UpdateSeekerDto } from "../models/seeker.model";

const seekerCollection = (): Collection<Seeker> => db.collection<Seeker>("seekers");

export const createSeeker = async (seekerData: CreateSeekerDto): Promise<Seeker> => {
	// Check if seeker with this appwriteId already exists
	const existingSeeker = await findByAppwriteId(seekerData.appwriteId);
	if (existingSeeker) {
		throw new Error("Seeker with this Appwrite ID already exists");
	}

	const newSeeker: Seeker = {
		...seekerData,
		phoneVerified: false,
		education: seekerData.education || [],
		workExperience: seekerData.workExperience || [],
		skills: seekerData.skills || [],
		languages: seekerData.languages || [],
		certifications: seekerData.certifications || [],
		portfolioLinks: seekerData.portfolioLinks || [],
		availability: seekerData.availability || {},
		preferences: seekerData.preferences || {},
		isActive: true,
		isDeleted: false,
		createdAt: new Date(),
		updatedAt: new Date(),
	};

	const result = await seekerCollection().insertOne(newSeeker);
	return { ...newSeeker, _id: result.insertedId.toString() };
};

export const findById = async (seekerId: string): Promise<Seeker | null> => {
	if (!ObjectId.isValid(seekerId)) {
		return null;
	}
	return await seekerCollection().findOne({
		_id: new ObjectId(seekerId),
		isDeleted: false,
	});
};

export const findByAppwriteId = async (appwriteId: string): Promise<Seeker | null> => {
	return await seekerCollection().findOne({
		appwriteId,
		isDeleted: false,
	});
};



export const updateSeeker = async (
	seekerId: string,
	updateData: UpdateSeekerDto
): Promise<Seeker | null> => {
	if (!ObjectId.isValid(seekerId)) {
		return null;
	}

	const updatedSeeker: any = {
		$set: {
			...updateData,
			updatedAt: new Date(),
		},
	};

	await seekerCollection().updateOne(
		{ _id: new ObjectId(seekerId), isDeleted: false },
		updatedSeeker
	);

	return await findById(seekerId);
};

export const updateSeekerByAppwriteId = async (
	appwriteId: string,
	updateData: UpdateSeekerDto
): Promise<Seeker | null> => {
	const updatedSeeker: any = {
		$set: {
			...updateData,
			updatedAt: new Date(),
		},
	};

	await seekerCollection().updateOne(
		{ appwriteId, isDeleted: false },
		updatedSeeker
	);

	return await findByAppwriteId(appwriteId);
};

export const deleteSeeker = async (seekerId: string): Promise<boolean> => {
	if (!ObjectId.isValid(seekerId)) {
		return false;
	}

	const result = await seekerCollection().updateOne(
		{ _id: new ObjectId(seekerId), isDeleted: false },
		{ $set: { isDeleted: true, updatedAt: new Date() } }
	);

	return result.modifiedCount > 0;
};

export const getAllSeekers = async (
	page: number = 1,
	limit: number = 10,
	filters: any = {}
): Promise<{ seekers: Seeker[]; total: number }> => {
	const skip = (page - 1) * limit;
	const query = { isDeleted: false, ...filters };

	const seekers = await seekerCollection()
		.find(query)
		.skip(skip)
		.limit(limit)
		.toArray();

	const total = await seekerCollection().countDocuments(query);

	return { seekers, total };
};

export const findSeekersBySkills = async (skills: string[]): Promise<Seeker[]> => {
	return await seekerCollection()
		.find({
			isDeleted: false,
			isActive: true,
			"skills.name": { $in: skills },
		})
		.toArray();
};

export const findSeekersByLocation = async (location: string): Promise<Seeker[]> => {
	return await seekerCollection()
		.find({
			isDeleted: false,
			isActive: true,
			$or: [
				{ "preferences.locations": { $in: [location] } },
				{ "preferences.remoteWork": true },
			],
		})
		.toArray();
};

export const updateFlnScore = async (
	seekerId: string,
	flnScore: number
): Promise<Seeker | null> => {
	if (!ObjectId.isValid(seekerId)) {
		return null;
	}

	await seekerCollection().updateOne(
		{ _id: new ObjectId(seekerId) },
		{
			$set: {
				flnScore,
				updatedAt: new Date(),
			},
		}
	);

	return await findById(seekerId);
};

export const findActiveSeekers = async (): Promise<Seeker[]> => {
	return await seekerCollection()
		.find({
			isDeleted: false,
			isActive: true,
		})
		.toArray();
};
