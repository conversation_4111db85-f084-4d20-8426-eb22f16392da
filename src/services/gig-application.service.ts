import { Collection, ObjectId } from "mongodb";
import { db } from "../config/db";
import { GigApplication, CreateGigApplicationDto, UpdateGigApplicationDto } from "../models/gig-application.model";

const gigApplicationCollection = (): Collection<GigApplication> => db.collection<GigApplication>("gig_applications");

export const createGigApplication = async (
	applicationData: CreateGigApplicationDto
): Promise<GigApplication> => {
	// Check if seeker has already applied for this gig
	const existingApplication = await findBySeekerAndGig(
		applicationData.seekerId,
		applicationData.gigId
	);
	if (existingApplication) {
		throw new Error("<PERSON><PERSON> has already applied for this gig");
	}

	const newApplication: GigApplication = {
		...applicationData,
		seekerId: new ObjectId(applicationData.seekerId),
		gigId: new ObjectId(applicationData.gigId),
		appliedAt: new Date(),
		status: "applied",
		files: applicationData.files || [],
		isDeleted: false,
		createdAt: new Date(),
		updatedAt: new Date(),
	};

	const result = await gigApplicationCollection().insertOne(newApplication);
	return { ...newApplication, _id: result.insertedId.toString() };
};

export const findById = async (applicationId: string): Promise<GigApplication | null> => {
	if (!ObjectId.isValid(applicationId)) {
		return null;
	}
	return await gigApplicationCollection().findOne({
		_id: new ObjectId(applicationId),
		isDeleted: false,
	});
};

export const findBySeekerAndGig = async (
	seekerId: string,
	gigId: string
): Promise<GigApplication | null> => {
	if (!ObjectId.isValid(seekerId) || !ObjectId.isValid(gigId)) {
		return null;
	}
	return await gigApplicationCollection().findOne({
		seekerId: new ObjectId(seekerId),
		gigId: new ObjectId(gigId),
		isDeleted: false,
	});
};

export const updateGigApplication = async (
	applicationId: string,
	updateData: UpdateGigApplicationDto
): Promise<GigApplication | null> => {
	if (!ObjectId.isValid(applicationId)) {
		return null;
	}

	const updatedApplication = {
		$set: {
			...updateData,
			updatedAt: new Date(),
		},
	};

	await gigApplicationCollection().updateOne(
		{ _id: new ObjectId(applicationId) },
		updatedApplication
	);

	return await findById(applicationId);
};

export const updateApplicationStatus = async (
	applicationId: string,
	status: "applied" | "under_review" | "shortlisted" | "interviewed" | "hired" | "rejected",
	reviewedBy?: string,
	notes?: string
): Promise<GigApplication | null> => {
	if (!ObjectId.isValid(applicationId)) {
		return null;
	}

	const updateData: any = {
		status,
		reviewedAt: new Date(),
		updatedAt: new Date(),
	};

	if (reviewedBy && ObjectId.isValid(reviewedBy)) {
		updateData.reviewedBy = new ObjectId(reviewedBy);
	}

	if (notes) {
		updateData.notes = notes;
	}

	await gigApplicationCollection().updateOne(
		{ _id: new ObjectId(applicationId) },
		{ $set: updateData }
	);

	return await findById(applicationId);
};

export const deleteGigApplication = async (applicationId: string): Promise<boolean> => {
	if (!ObjectId.isValid(applicationId)) {
		return false;
	}

	const result = await gigApplicationCollection().updateOne(
		{ _id: new ObjectId(applicationId) },
		{ $set: { isDeleted: true, updatedAt: new Date() } }
	);

	return result.modifiedCount > 0;
};

export const findApplicationsBySeeker = async (seekerId: string): Promise<GigApplication[]> => {
	if (!ObjectId.isValid(seekerId)) {
		return [];
	}

	return await gigApplicationCollection()
		.find({
			seekerId: new ObjectId(seekerId),
			isDeleted: false,
		})
		.sort({ appliedAt: -1 })
		.toArray();
};

export const findApplicationsByGig = async (gigId: string): Promise<GigApplication[]> => {
	if (!ObjectId.isValid(gigId)) {
		return [];
	}

	return await gigApplicationCollection()
		.find({
			gigId: new ObjectId(gigId),
			isDeleted: false,
		})
		.sort({ appliedAt: -1 })
		.toArray();
};

export const findApplicationsByProvider = async (
	providerGigIds: string[]
): Promise<GigApplication[]> => {
	const validGigIds = providerGigIds
		.filter(id => ObjectId.isValid(id))
		.map(id => new ObjectId(id));

	if (validGigIds.length === 0) {
		return [];
	}

	return await gigApplicationCollection()
		.find({
			gigId: { $in: validGigIds },
			isDeleted: false,
		})
		.sort({ appliedAt: -1 })
		.toArray();
};

export const findApplicationsByStatus = async (
	status: "applied" | "under_review" | "shortlisted" | "interviewed" | "hired" | "rejected"
): Promise<GigApplication[]> => {
	return await gigApplicationCollection()
		.find({
			status: status as any,
			isDeleted: false,
		})
		.sort({ appliedAt: -1 })
		.toArray();
};

export const getApplicationStatistics = async (gigId?: string): Promise<{
	total: number;
	byStatus: { status: string; count: number }[];
}> => {
	const matchStage: any = { isDeleted: false };
	if (gigId && ObjectId.isValid(gigId)) {
		matchStage.gigId = new ObjectId(gigId);
	}

	const pipeline = [
		{ $match: matchStage },
		{
			$group: {
				_id: "$status",
				count: { $sum: 1 },
			},
		},
	];

	const statusCounts = await gigApplicationCollection().aggregate(pipeline).toArray();
	const total = await gigApplicationCollection().countDocuments(matchStage);

	const byStatus = statusCounts.map(item => ({
		status: item._id,
		count: item.count,
	}));

	return { total, byStatus };
};

export const findRecentApplications = async (
	limit: number = 10
): Promise<GigApplication[]> => {
	return await gigApplicationCollection()
		.find({ isDeleted: false })
		.sort({ appliedAt: -1 })
		.limit(limit)
		.toArray();
};

export const withdrawApplication = async (
	applicationId: string,
	seekerId: string
): Promise<boolean> => {
	if (!ObjectId.isValid(applicationId) || !ObjectId.isValid(seekerId)) {
		return false;
	}

	const result = await gigApplicationCollection().updateOne(
		{
			_id: new ObjectId(applicationId),
			seekerId: new ObjectId(seekerId),
			status: { $in: ["applied" as any, "under_review" as any] }, // Can only withdraw if not yet processed
		},
		{
			$set: {
				status: "withdrawn" as any,
				updatedAt: new Date(),
			},
		}
	);

	return result.modifiedCount > 0;
};
