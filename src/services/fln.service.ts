import { Collection, ObjectId } from "mongodb";
import { db } from "../config/db";
import { Fln, FlnRecord, FlnDetails } from "../models/fln.model";

const flnCollection = (): Collection<Fln> => db.collection<Fln>("fln_scores");

export const createFlnRecord = async (
	seekerId: string,
	flnScore: number,
	details: FlnDetails
): Promise<Fln> => {
	if (!ObjectId.isValid(seekerId)) {
		throw new Error("Invalid seeker ID");
	}

	// Check if FLN record already exists for this seeker
	const existingFln = await findBySeekerIdActive(seekerId);

	const newFlnRecord: FlnRecord = {
		score: flnScore,
		details,
		testDate: new Date(),
		validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // Valid for 1 year
	};

	if (existingFln) {
		// Update existing record
		const updatedFln = await flnCollection().updateOne(
			{ _id: new ObjectId(existingFln._id) },
			{
				$set: {
					flnScore,
					details,
					updatedAt: new Date(),
				},
				$push: {
					fluencyRecord: newFlnRecord,
				},
			}
		);

		return await findBySeekerIdActive(seekerId) as Fln;
	} else {
		// Create new FLN record
		const newFln: Fln = {
			seekerId: new ObjectId(seekerId),
			flnScore,
			details,
			fluencyRecord: [newFlnRecord],
			isDeleted: false,
			createdAt: new Date(),
			updatedAt: new Date(),
		};

		const result = await flnCollection().insertOne(newFln);
		return { ...newFln, _id: result.insertedId.toString() };
	}
};

export const findBySeekerIdActive = async (seekerId: string): Promise<Fln | null> => {
	if (!ObjectId.isValid(seekerId)) {
		return null;
	}

	return await flnCollection().findOne({
		seekerId: new ObjectId(seekerId),
		isDeleted: false,
	});
};

export const findById = async (flnId: string): Promise<Fln | null> => {
	if (!ObjectId.isValid(flnId)) {
		return null;
	}
	return await flnCollection().findOne({
		_id: new ObjectId(flnId),
		isDeleted: false,
	});
};

export const updateFlnScore = async (
	seekerId: string,
	flnScore: number,
	details: FlnDetails
): Promise<Fln | null> => {
	if (!ObjectId.isValid(seekerId)) {
		return null;
	}

	const newFlnRecord: FlnRecord = {
		score: flnScore,
		details,
		testDate: new Date(),
		validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // Valid for 1 year
	};

	await flnCollection().updateOne(
		{ seekerId: new ObjectId(seekerId) },
		{
			$set: {
				flnScore,
				details,
				updatedAt: new Date(),
			},
			$push: {
				fluencyRecord: newFlnRecord,
			},
		}
	);

	return await findBySeekerIdActive(seekerId);
};

export const getFlnHistory = async (seekerId: string): Promise<FlnRecord[]> => {
	if (!ObjectId.isValid(seekerId)) {
		return [];
	}

	const flnRecord = await flnCollection().findOne({
		seekerId: new ObjectId(seekerId),
		isDeleted: false,
	});

	return flnRecord?.fluencyRecord || [];
};

export const deleteFlnRecord = async (flnId: string): Promise<boolean> => {
	if (!ObjectId.isValid(flnId)) {
		return false;
	}

	const result = await flnCollection().updateOne(
		{ _id: new ObjectId(flnId) },
		{ $set: { isDeleted: true, updatedAt: new Date() } }
	);

	return result.modifiedCount > 0;
};

export const getFlnStatistics = async (): Promise<{
	averageScore: number;
	totalRecords: number;
	scoreDistribution: { range: string; count: number }[];
}> => {
	const pipeline = [
		{ $match: { isDeleted: false } },
		{
			$group: {
				_id: null,
				averageScore: { $avg: "$flnScore" },
				totalRecords: { $sum: 1 },
				scores: { $push: "$flnScore" },
			},
		},
	];

	const result = await flnCollection().aggregate(pipeline).toArray();
	
	if (result.length === 0) {
		return {
			averageScore: 0,
			totalRecords: 0,
			scoreDistribution: [],
		};
	}

	const { averageScore, totalRecords, scores } = result[0];

	// Calculate score distribution
	const scoreDistribution = [
		{ range: "0-20", count: scores.filter((s: number) => s >= 0 && s < 20).length },
		{ range: "20-40", count: scores.filter((s: number) => s >= 20 && s < 40).length },
		{ range: "40-60", count: scores.filter((s: number) => s >= 40 && s < 60).length },
		{ range: "60-80", count: scores.filter((s: number) => s >= 60 && s < 80).length },
		{ range: "80-100", count: scores.filter((s: number) => s >= 80 && s <= 100).length },
	];

	return {
		averageScore: Math.round(averageScore * 100) / 100,
		totalRecords,
		scoreDistribution,
	};
};

export const findSeekersByFlnRange = async (
	minScore: number,
	maxScore: number
): Promise<Fln[]> => {
	return await flnCollection()
		.find({
			isDeleted: false,
			flnScore: { $gte: minScore, $lte: maxScore },
		})
		.sort({ flnScore: -1 })
		.toArray();
};

export const getLatestFlnRecord = async (seekerId: string): Promise<FlnRecord | null> => {
	if (!ObjectId.isValid(seekerId)) {
		return null;
	}

	const flnRecord = await flnCollection().findOne({
		seekerId: new ObjectId(seekerId),
		isDeleted: false,
	});

	if (!flnRecord || !flnRecord.fluencyRecord || flnRecord.fluencyRecord.length === 0) {
		return null;
	}

	// Return the most recent record
	return flnRecord.fluencyRecord.sort((a, b) =>
		new Date(b.testDate).getTime() - new Date(a.testDate).getTime()
	)[0];
};
