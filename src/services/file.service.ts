import { Collection, ObjectId } from "mongodb";
import { db } from "../config/db";
import { File, CreateFileDto, UpdateFileDto, FileType } from "../models/file.model";

const fileCollection = (): Collection<File> => db.collection<File>("files");

export const createFile = async (fileData: CreateFileDto): Promise<File> => {
	// Check if file with this fileId already exists
	const existingFile = await findByFileId(fileData.fileId);
	if (existingFile) {
		throw new Error("File with this ID already exists");
	}

	const newFile: File = {
		...fileData,
		uploadedBy: new ObjectId(fileData.userId),
		uploaderType: fileData.uploaderType || "seeker", // Default to seeker if not specified
		active: true,
		isDeleted: false,
		uploadedAt: new Date(),
		createdAt: new Date(),
		updatedAt: new Date(),
		metadata: fileData.metadata || {},
	};

	const result = await fileCollection().insertOne(newFile);
	return { ...newFile, _id: result.insertedId.toString() };
};

export const findById = async (fileId: string): Promise<File | null> => {
	if (!ObjectId.isValid(fileId)) {
		return null;
	}
	return await fileCollection().findOne({
		_id: new ObjectId(fileId),
		isDeleted: false,
	});
};

export const findByFileId = async (fileId: string): Promise<File | null> => {
	return await fileCollection().findOne({
		fileId,
		isDeleted: false,
	});
};

export const updateFile = async (
	fileId: string,
	updateData: UpdateFileDto
): Promise<File | null> => {
	if (!ObjectId.isValid(fileId)) {
		return null;
	}

	const updatedFile = {
		$set: {
			...updateData,
			updatedAt: new Date(),
		},
	};

	await fileCollection().updateOne(
		{ _id: new ObjectId(fileId) },
		updatedFile
	);

	return await findById(fileId);
};

export const deleteFile = async (fileId: string): Promise<boolean> => {
	if (!ObjectId.isValid(fileId)) {
		return false;
	}

	const result = await fileCollection().updateOne(
		{ _id: new ObjectId(fileId) },
		{ $set: { isDeleted: true, active: false, updatedAt: new Date() } }
	);

	return result.modifiedCount > 0;
};

export const findFilesByUploader = async (
	uploaderId: string,
	uploaderType: "seeker" | "provider"
): Promise<File[]> => {
	if (!ObjectId.isValid(uploaderId)) {
		return [];
	}

	return await fileCollection()
		.find({
			uploadedBy: new ObjectId(uploaderId),
			uploaderType,
			isDeleted: false,
		})
		.sort({ uploadedAt: -1 })
		.toArray();
};

export const findFilesByType = async (
	fileType: FileType,
	uploaderId?: string
): Promise<File[]> => {
	const query: any = {
		fileType,
		isDeleted: false,
		active: true,
	};

	if (uploaderId && ObjectId.isValid(uploaderId)) {
		query.uploadedBy = new ObjectId(uploaderId);
	}

	return await fileCollection()
		.find(query)
		.sort({ uploadedAt: -1 })
		.toArray();
};

export const findActiveFiles = async (
	page: number = 1,
	limit: number = 10,
	filters: any = {}
): Promise<{ files: File[]; total: number }> => {
	const skip = (page - 1) * limit;
	const query = { isDeleted: false, active: true, ...filters };

	const files = await fileCollection()
		.find(query)
		.skip(skip)
		.limit(limit)
		.sort({ uploadedAt: -1 })
		.toArray();

	const total = await fileCollection().countDocuments(query);

	return { files, total };
};

export const deactivateFile = async (fileId: string): Promise<boolean> => {
	if (!ObjectId.isValid(fileId)) {
		return false;
	}

	const result = await fileCollection().updateOne(
		{ _id: new ObjectId(fileId) },
		{
			$set: {
				active: false,
				updatedAt: new Date(),
			},
		}
	);

	return result.modifiedCount > 0;
};

export const activateFile = async (fileId: string): Promise<boolean> => {
	if (!ObjectId.isValid(fileId)) {
		return false;
	}

	const result = await fileCollection().updateOne(
		{ _id: new ObjectId(fileId) },
		{
			$set: {
				active: true,
				updatedAt: new Date(),
			},
		}
	);

	return result.modifiedCount > 0;
};

export const findFilesByMimeType = async (mimeType: string): Promise<File[]> => {
	return await fileCollection()
		.find({
			mimeType,
			isDeleted: false,
			active: true,
		})
		.sort({ uploadedAt: -1 })
		.toArray();
};

export const getFileStatistics = async (): Promise<{
	totalFiles: number;
	totalSize: number;
	byType: { type: string; count: number; totalSize: number }[];
	byUploader: { type: string; count: number }[];
}> => {
	const totalFiles = await fileCollection().countDocuments({
		isDeleted: false,
		active: true,
	});

	const sizeResult = await fileCollection()
		.aggregate([
			{ $match: { isDeleted: false, active: true } },
			{ $group: { _id: null, totalSize: { $sum: "$size" } } },
		])
		.toArray();

	const totalSize = sizeResult.length > 0 ? sizeResult[0].totalSize : 0;

	const byTypeResult = await fileCollection()
		.aggregate([
			{ $match: { isDeleted: false, active: true } },
			{
				$group: {
					_id: "$fileType",
					count: { $sum: 1 },
					totalSize: { $sum: "$size" },
				},
			},
		])
		.toArray();

	const byType = byTypeResult.map(item => ({
		type: item._id,
		count: item.count,
		totalSize: item.totalSize,
	}));

	const byUploaderResult = await fileCollection()
		.aggregate([
			{ $match: { isDeleted: false, active: true } },
			{
				$group: {
					_id: "$uploaderType",
					count: { $sum: 1 },
				},
			},
		])
		.toArray();

	const byUploader = byUploaderResult.map(item => ({
		type: item._id,
		count: item.count,
	}));

	return {
		totalFiles,
		totalSize,
		byType,
		byUploader,
	};
};

export const searchFiles = async (
	searchTerm: string,
	page: number = 1,
	limit: number = 10
): Promise<{ files: File[]; total: number }> => {
	const skip = (page - 1) * limit;
	const searchRegex = { $regex: searchTerm, $options: "i" };

	const query = {
		isDeleted: false,
		active: true,
		$or: [
			{ fileName: searchRegex },
			{ "metadata.description": searchRegex },
			{ "metadata.tags": { $in: [searchRegex] } },
		],
	};

	const files = await fileCollection()
		.find(query)
		.skip(skip)
		.limit(limit)
		.sort({ uploadedAt: -1 })
		.toArray();

	const total = await fileCollection().countDocuments(query);

	return { files, total };
};
