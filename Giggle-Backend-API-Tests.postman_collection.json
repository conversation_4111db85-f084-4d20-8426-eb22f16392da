{"info": {"name": "Giggle Backend API Tests", "description": "Complete API testing collection for Giggle Backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:5000", "type": "string"}], "item": [{"name": "1. Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/users/verify-user", "host": ["{{base_url}}"], "path": ["api", "users", "verify-user"]}}}, {"name": "2. Create Provider", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"phoneNumber\": \"+**********\",\n  \"roleTitle\": \"Business Owner\"\n}"}, "url": {"raw": "{{base_url}}/api/test/provider", "host": ["{{base_url}}"], "path": ["api", "test", "provider"]}}}, {"name": "3. <PERSON> Provider", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/test/provider", "host": ["{{base_url}}"], "path": ["api", "test", "provider"]}}}, {"name": "4. Get All Providers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/test/providers?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "test", "providers"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "5. <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"phoneNumber\": \"+**********\",\n  \"educationLevel\": \"bachelor\",\n  \"employmentStatus\": \"unemployed\"\n}"}, "url": {"raw": "{{base_url}}/api/test/seeker", "host": ["{{base_url}}"], "path": ["api", "test", "seeker"]}}}, {"name": "6. <PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/test/seeker", "host": ["{{base_url}}"], "path": ["api", "test", "seeker"]}}}, {"name": "7. Get All Seekers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/test/seekers?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "test", "seekers"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "8. Create Company", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"companyName\": \"Tech Innovations Inc\",\n  \"industry\": \"technology\",\n  \"companySize\": \"startup\",\n  \"description\": \"A cutting-edge technology company\",\n  \"website\": \"https://techinnovations.com\"\n}"}, "url": {"raw": "{{base_url}}/api/test/company", "host": ["{{base_url}}"], "path": ["api", "test", "company"]}}}, {"name": "9. <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Senior Full Stack Developer\",\n  \"description\": \"We are looking for an experienced full stack developer\",\n  \"salary\": {\n    \"min\": 5000,\n    \"max\": 8000,\n    \"currency\": \"USD\",\n    \"period\": \"monthly\"\n  },\n  \"jobType\": \"full-time\",\n  \"jobTrait\": \"remote\",\n  \"positionOffered\": \"Senior Developer\",\n  \"minimumQualificationRequired\": \"Bachelor's in Computer Science\",\n  \"experience\": {\n    \"min\": 3,\n    \"max\": 5,\n    \"description\": \"3-5 years of full stack development\"\n  },\n  \"specialization\": [\"React\", \"Node.js\", \"MongoDB\"],\n  \"facilities\": [\"Health Insurance\", \"Remote Work\", \"Flexible Hours\"]\n}"}, "url": {"raw": "{{base_url}}/api/test/gig", "host": ["{{base_url}}"], "path": ["api", "test", "gig"]}}}, {"name": "10. Get All Gigs", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/test/gigs?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "test", "gigs"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}]}